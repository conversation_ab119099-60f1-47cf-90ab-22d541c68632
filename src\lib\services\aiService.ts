// Serviço para integração com Firebase Functions de IA

export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
  isFavorite?: boolean;
}

export interface StreamResponse {
  content: string;
  isComplete: boolean;
  error?: string;
}

export interface AIServiceConfig {
  username: string;
  chatId: string;
  message: string;
  model?: string;
}

class AIService {
  private readonly functionUrl = 'https://us-central1-rafthor-0001.cloudfunctions.net/chatWithAI';
  private abortController: AbortController | null = null;

  /**
   * Envia mensagem para a IA com streaming
   */
  async sendMessage(
    config: AIServiceConfig,
    onChunk: (chunk: string) => void,
    onComplete: (fullResponse: string) => void,
    onError: (error: string) => void
  ): Promise<void> {
    try {
      // Criar novo AbortController para esta requisição
      this.abortController = new AbortController();

      // Preparar dados da requisição
      const requestData = {
        username: config.username,
        chatId: config.chatId,
        message: config.message,
        model: config.model,
      };

      // DEBUG: Log detalhado no console do navegador
      console.group('🤖 AI SERVICE - ENVIANDO REQUISIÇÃO');
      console.log('📡 URL da Function:', this.functionUrl);
      console.log('📋 Dados da Requisição:', {
        username: requestData.username,
        chatId: requestData.chatId,
        message: requestData.message,
        messageLength: requestData.message?.length || 0,
        messagePreview: requestData.message?.substring(0, 100) + (requestData.message?.length > 100 ? '...' : ''),
        model: requestData.model,
        timestamp: new Date().toISOString()
      });
      console.log('🔧 Headers:', {
        'Content-Type': 'application/json',
        'Method': 'POST'
      });
      console.log('📦 Body JSON:', JSON.stringify(requestData, null, 2));
      console.log('📊 Estatísticas:', {
        bodySize: JSON.stringify(requestData).length + ' bytes',
        hasAbortController: !!this.abortController,
        userAgent: navigator.userAgent
      });

      // Buscar configurações do chat do Firestore para mostrar nos logs
      console.log('🔍 Buscando configurações do chat do Firestore para debug...');
      try {
        // Importar Firebase dinamicamente para evitar problemas de SSR
        const { doc, getDoc } = await import('firebase/firestore');
        const { db } = await import('@/lib/firebase');

        const chatDocRef = doc(db, 'usuarios', requestData.username, 'conversas', requestData.chatId);
        const chatDocSnap = await getDoc(chatDocRef);

        if (chatDocSnap.exists()) {
          const chatConfig = chatDocSnap.data();
          console.group('⚙️ CONFIGURAÇÕES DO CHAT (FIRESTORE)');
          console.log('🎯 System Prompt:', {
            length: chatConfig.systemPrompt?.length || 0,
            content: chatConfig.systemPrompt || '(vazio)',
            preview: chatConfig.systemPrompt?.substring(0, 200) + (chatConfig.systemPrompt?.length > 200 ? '...' : '')
          });
          console.log('📝 Contexto:', {
            length: chatConfig.context?.length || 0,
            content: chatConfig.context || '(vazio)',
            preview: chatConfig.context?.substring(0, 200) + (chatConfig.context?.length > 200 ? '...' : '')
          });
          console.log('🌡️ Parâmetros de Geração:', {
            temperature: chatConfig.temperature,
            temperatureType: typeof chatConfig.temperature,
            frequencyPenalty: chatConfig.frequencyPenalty,
            frequencyPenaltyType: typeof chatConfig.frequencyPenalty,
            repetitionPenalty: chatConfig.repetitionPenalty,
            repetitionPenaltyType: typeof chatConfig.repetitionPenalty,
            maxTokens: chatConfig.maxTokens,
            maxTokensType: typeof chatConfig.maxTokens,
            latexInstructions: chatConfig.latexInstructions
          });
          console.log('📊 Metadados:', {
            lastUsedModel: chatConfig.lastUsedModel,
            createdAt: chatConfig.createdAt,
            lastUpdatedAt: chatConfig.lastUpdatedAt,
            name: chatConfig.name
          });
          console.groupEnd();
        } else {
          console.warn('⚠️ Documento do chat não encontrado no Firestore');
        }
      } catch (error) {
        console.warn('⚠️ Não foi possível buscar configurações do chat do Firestore:', error);
      }

      // Buscar histórico de mensagens do Storage para mostrar nos logs
      console.log('🔍 Buscando histórico de mensagens do Storage para debug...');
      try {
        const messagesResponse = await fetch(`/api/chat/${requestData.username}/${requestData.chatId}`);
        if (messagesResponse.ok) {
          const chatData = await messagesResponse.json();
          const messages = chatData.messages || [];
          console.group('💬 HISTÓRICO DE MENSAGENS (STORAGE)');
          console.log('📊 Total de mensagens:', messages.length);

          if (messages.length > 0) {
            console.log('📋 Últimas 5 mensagens:', messages.slice(-5).map((msg: any, index: number) => ({
              index: messages.length - 5 + index,
              id: msg.id,
              role: msg.role,
              contentLength: msg.content?.length || 0,
              contentPreview: msg.content?.substring(0, 100) + (msg.content?.length > 100 ? '...' : ''),
              timestamp: msg.timestamp,
              hasAttachments: !!(msg.attachments && msg.attachments.length > 0),
              attachmentCount: msg.attachments?.length || 0
            })));

            if (messages.length > 5) {
              console.log('📜 Mensagens mais antigas:', `${messages.length - 5} mensagens anteriores não mostradas`);
            }

            // Estatísticas do histórico
            const userMessages = messages.filter((msg: any) => msg.role === 'user').length;
            const assistantMessages = messages.filter((msg: any) => msg.role === 'assistant').length;
            const totalChars = messages.reduce((acc: number, msg: any) => acc + (msg.content?.length || 0), 0);

            console.log('📈 Estatísticas do Histórico:', {
              totalMessages: messages.length,
              userMessages,
              assistantMessages,
              totalCharacters: totalChars,
              averageMessageLength: messages.length > 0 ? Math.round(totalChars / messages.length) : 0,
              oldestMessage: messages[0]?.timestamp,
              newestMessage: messages[messages.length - 1]?.timestamp
            });
          } else {
            console.log('📭 Nenhuma mensagem encontrada no histórico');
          }

          console.groupEnd();
        } else {
          console.warn('⚠️ Falha ao buscar mensagens - Status:', messagesResponse.status);
        }
      } catch (error) {
        console.warn('⚠️ Não foi possível buscar histórico de mensagens:', error);
      }

      console.groupEnd();

      const response = await fetch(this.functionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
        signal: this.abortController.signal,
      });

      // DEBUG: Log da resposta
      console.group('📡 AI SERVICE - RESPOSTA RECEBIDA');
      console.log('✅ Status:', response.status, response.statusText);
      console.log('📋 Headers:', Object.fromEntries(response.headers.entries()));
      console.log('🔍 Response OK:', response.ok);
      console.log('🌐 URL:', response.url);
      console.groupEnd();

      if (!response.ok) {
        const errorData = await response.json();
        console.group('❌ AI SERVICE - ERRO NA RESPOSTA');
        console.error('Status:', response.status);
        console.error('Status Text:', response.statusText);
        console.error('Error Data:', errorData);
        console.groupEnd();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      if (!response.body) {
        throw new Error('Response body is not available');
      }

      // Processar stream
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullResponse = '';

      console.group('🔄 AI SERVICE - PROCESSANDO STREAM');
      console.log('📖 Reader criado:', !!reader);
      console.log('⏰ Início do stream:', new Date().toISOString());

      let chunkCount = 0;
      let totalBytes = 0;

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            console.log('✅ Stream finalizado');
            console.log('📊 Estatísticas finais:', {
              totalChunks: chunkCount,
              totalBytes: totalBytes,
              responseLength: fullResponse.length,
              avgChunkSize: chunkCount > 0 ? Math.round(totalBytes / chunkCount) : 0
            });
            console.groupEnd();
            break;
          }

          chunkCount++;
          totalBytes += value?.length || 0;

          // DEBUG: Log detalhado dos bytes brutos
          if (chunkCount <= 10) {
            console.log(`🔍 Chunk ${chunkCount} - Bytes brutos:`, {
              rawBytes: Array.from(value || []).map(b => b.toString(16).padStart(2, '0')).join(' '),
              byteLength: value?.length || 0,
              firstBytes: Array.from(value?.slice(0, 10) || []),
              lastBytes: Array.from(value?.slice(-10) || [])
            });
          }

          let chunk = decoder.decode(value, { stream: true });

          // REMOÇÃO COMPLETA DE MARKDOWN: Limpar todos os caracteres de formatação
          const originalChunk = chunk;
          chunk = chunk
            // Remove caracteres de controle
            .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
            // Remove espaços não-quebráveis
            .replace(/\u00A0/g, ' ')
            // Remove caracteres de substituição (corrupção)
            .replace(/\uFFFD/g, '')
            // REMOVE MARKDOWN COMPLETAMENTE:
            // Remove LaTeX/MathJax
            .replace(/\$\$[\s\S]*?\$\$/g, '[FÓRMULA MATEMÁTICA REMOVIDA]')
            .replace(/\$[^$]*?\$/g, '[EXPRESSÃO MATEMÁTICA REMOVIDA]')
            .replace(/\\\[[\s\S]*?\\\]/g, '[FÓRMULA MATEMÁTICA REMOVIDA]')
            .replace(/\\\([\s\S]*?\\\)/g, '[EXPRESSÃO MATEMÁTICA REMOVIDA]')
            // Remove markdown básico
            .replace(/\*\*(.*?)\*\*/g, '$1') // Bold
            .replace/\*(.*?)\*/g, '$1') // Italic
            .replace(/`(.*?)`/g, '$1') // Code inline
            .replace(/```[\s\S]*?```/g, '[CÓDIGO REMOVIDO]') // Code blocks
            .replace(/#{1,6}\s*(.*)/g, '$1') // Headers
            .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Links
            .replace(/!\[(.*?)\]\(.*?\)/g, '[IMAGEM: $1]') // Images
            // Remove listas
            .replace(/^\s*[-*+]\s+/gm, '')
            .replace(/^\s*\d+\.\s+/gm, '')
            // Remove tabelas
            .replace(/\|.*\|/g, '[TABELA REMOVIDA]')
            // Remove quebras de linha excessivas
            .replace(/\n{3,}/g, '\n\n')
            // Remove espaços extras
            .replace(/[ \t]+/g, ' ')
            .trim();

          if (originalChunk !== chunk) {
            console.warn(`🧹 Chunk ${chunkCount} - Markdown removido:`, {
              original: originalChunk.substring(0, 100) + '...',
              cleaned: chunk.substring(0, 100) + '...',
              originalLength: originalChunk.length,
              cleanedLength: chunk.length,
              reductionPercent: Math.round((1 - chunk.length / originalChunk.length) * 100) + '%'
            });
          }

          // DEBUG: Log detalhado do chunk decodificado
          if (chunkCount <= 10) {
            console.log(`📦 Chunk ${chunkCount} - Decodificado:`, {
              size: chunk.length,
              content: chunk,
              contentPreview: chunk.substring(0, 100) + (chunk.length > 100 ? '...' : ''),
              charCodes: Array.from(chunk).map(c => c.charCodeAt(0)),
              hasNonAscii: /[^\x00-\x7F]/.test(chunk),
              hasControlChars: /[\x00-\x1F\x7F-\x9F]/.test(chunk),
              timestamp: new Date().toISOString()
            });
          }

          fullResponse += chunk;

          // DEBUG: Detectar conteúdo suspeito
          const suspiciousPatterns = [
            /[\u4e00-\u9fff]/, // Caracteres chineses
            /[\u3040-\u309f\u30a0-\u30ff]/, // Japonês (hiragana/katakana)
            /[\u0400-\u04ff]/, // Cirílico
            /[\u0590-\u05ff]/, // Hebraico
            /[\u0600-\u06ff]/, // Árabe
            /[^\x00-\x7F\u00C0-\u017F\u0100-\u024F]/, // Caracteres não-latinos suspeitos
            /\uFFFD/, // Caractere de substituição (indica corrupção)
            /[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/ // Caracteres de controle
          ];

          const hasSuspiciousContent = suspiciousPatterns.some(pattern => pattern.test(chunk));

          if (hasSuspiciousContent) {
            console.group('🚨 CONTEÚDO SUSPEITO DETECTADO!');
            console.error('Chunk suspeito:', chunk);
            console.error('Chunk number:', chunkCount);
            console.error('Bytes brutos:', Array.from(value || []).map(b => b.toString(16).padStart(2, '0')).join(' '));
            console.error('Char codes:', Array.from(chunk).map(c => c.charCodeAt(0)));
            console.error('Full response até agora:', fullResponse);
            console.groupEnd();
          }

          // Chamar callback para cada chunk
          onChunk(chunk);
        }

        console.group('✅ AI SERVICE - RESPOSTA COMPLETA');
        console.log('📝 Resposta final:', {
          length: fullResponse.length,
          preview: fullResponse.substring(0, 200) + (fullResponse.length > 200 ? '...' : ''),
          wordCount: fullResponse.split(' ').length,
          timestamp: new Date().toISOString()
        });
        console.groupEnd();

        // Chamar callback de conclusão
        onComplete(fullResponse);

      } finally {
        reader.releaseLock();
      }

    } catch (error) {
      console.group('❌ AI SERVICE - ERRO CAPTURADO');
      console.error('🚨 Tipo do erro:', error instanceof Error ? error.constructor.name : typeof error);
      console.error('📝 Mensagem:', error instanceof Error ? error.message : String(error));
      console.error('🔍 Stack trace:', error instanceof Error ? error.stack : 'N/A');
      console.error('⏰ Timestamp:', new Date().toISOString());

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.log('🛑 Requisição cancelada pelo usuário');
          console.groupEnd();
          return;
        }
        console.error('🔧 Detalhes completos:', error);
        console.groupEnd();
        onError(error.message);
      } else {
        console.error('🔧 Detalhes completos:', error);
        console.groupEnd();
        onError('Erro desconhecido na comunicação com a IA');
      }
    } finally {
      this.abortController = null;
    }
  }

  /**
   * Cancela a requisição em andamento
   */
  cancelRequest(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
  }

  /**
   * Verifica se há uma requisição em andamento
   */
  isRequestInProgress(): boolean {
    return this.abortController !== null;
  }

  /**
   * Carrega mensagens de um chat usando a API route
   */
  async loadChatMessages(username: string, chatId: string): Promise<ChatMessage[]> {
    try {
      const response = await fetch(`/api/chat/${username}/${chatId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Erro ao carregar chat: ${response.statusText}`);
      }

      const chatData = await response.json();
      return chatData.messages || [];

    } catch (error) {
      console.error('Erro ao carregar mensagens do chat:', error);
      return [];
    }
  }

  /**
   * Converte mensagens do formato interno para o formato da IA
   */
  convertToAIFormat(messages: any[]): ChatMessage[] {
    return messages.map(msg => ({
      id: msg.id,
      content: msg.content,
      role: msg.sender === 'user' ? 'user' : 'assistant',
      timestamp: msg.timestamp,
      isFavorite: msg.isFavorite || false,
    }));
  }

  /**
   * Converte mensagens do formato da IA para o formato interno
   */
  convertFromAIFormat(messages: ChatMessage[]): any[] {
    return messages.map(msg => ({
      id: msg.id,
      content: msg.content,
      sender: msg.role === 'user' ? 'user' : 'ai',
      timestamp: msg.timestamp,
      isFavorite: msg.isFavorite || false,
    }));
  }

  /**
   * Gera ID único para mensagens
   */
  generateMessageId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  /**
   * Valida configuração antes de enviar
   */
  private validateConfig(config: AIServiceConfig): void {
    if (!config.username?.trim()) {
      throw new Error('Username é obrigatório');
    }
    if (!config.chatId?.trim()) {
      throw new Error('Chat ID é obrigatório');
    }
    if (!config.message?.trim()) {
      throw new Error('Mensagem é obrigatória');
    }
    if (config.message.length > 10000) {
      throw new Error('Mensagem muito longa (máximo 10.000 caracteres)');
    }
  }

  /**
   * Envia mensagem com validação
   */
  async sendMessageSafe(
    config: AIServiceConfig,
    onChunk: (chunk: string) => void,
    onComplete: (fullResponse: string) => void,
    onError: (error: string) => void
  ): Promise<void> {
    console.group('🚀 AI SERVICE - SEND MESSAGE SAFE');
    console.log('🔧 Config recebida:', {
      username: config.username,
      chatId: config.chatId,
      messageLength: config.message?.length || 0,
      model: config.model,
      timestamp: new Date().toISOString()
    });
    console.log('📋 Callbacks:', {
      hasOnChunk: typeof onChunk === 'function',
      hasOnComplete: typeof onComplete === 'function',
      hasOnError: typeof onError === 'function'
    });

    try {
      console.log('✅ Validando configuração...');
      this.validateConfig(config);
      console.log('✅ Configuração válida, enviando mensagem...');
      await this.sendMessage(config, onChunk, onComplete, onError);
      console.log('✅ Mensagem enviada com sucesso');
      console.groupEnd();
    } catch (error) {
      console.group('❌ ERRO EM SEND MESSAGE SAFE');
      console.error('🚨 Erro capturado:', error);
      console.error('📝 Tipo:', error instanceof Error ? error.constructor.name : typeof error);
      console.groupEnd();

      if (error instanceof Error) {
        onError(error.message);
      } else {
        onError('Erro de validação');
      }
      console.groupEnd();
    }
  }

  /**
   * Deleta uma mensagem do chat no Firebase Storage
   */
  async deleteMessage(username: string, chatId: string, messageId: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/chat/${username}/${chatId}/message/${messageId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Erro ao deletar mensagem: ${response.statusText}`);
      }

      return true;
    } catch (error) {
      console.error('Erro ao deletar mensagem:', error);
      return false;
    }
  }

  /**
   * Atualiza uma mensagem no chat no Firebase Storage
   */
  async updateMessage(username: string, chatId: string, messageId: string, newContent: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/chat/${username}/${chatId}/message/${messageId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: newContent }),
      });

      if (!response.ok) {
        throw new Error(`Erro ao atualizar mensagem: ${response.statusText}`);
      }

      return true;
    } catch (error) {
      console.error('Erro ao atualizar mensagem:', error);
      return false;
    }
  }
}

// Exportar instância singleton
export const aiService = new AIService();
export default aiService;
